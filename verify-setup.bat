@echo off
echo ========================================
echo  LiquidBounce Legacy - Setup Verification
echo ========================================
echo.

echo Checking project structure...
echo.

echo [1/6] Checking Gradle build file...
if exist "build.gradle" (
    echo ✓ build.gradle found
    findstr /C:"buildAndTest" build.gradle >nul
    if !errorlevel! equ 0 (
        echo ✓ buildAndTest task configured
    ) else (
        echo ✗ buildAndTest task not found
    )
) else (
    echo ✗ build.gradle not found
)

echo.
echo [2/6] Checking IntelliJ configurations...
if exist ".idea\runConfigurations\Build_and_Test.xml" (
    echo ✓ IntelliJ Build and Test configuration found
) else (
    echo ✗ IntelliJ configuration missing
)

if exist ".idea\gradle.xml" (
    echo ✓ Gradle integration configured
) else (
    echo ✗ Gradle integration missing
)

echo.
echo [3/6] Checking build scripts...
if exist "build-and-test.bat" (
    echo ✓ Windows build script found
) else (
    echo ✗ Windows build script missing
)

if exist "build-and-test.sh" (
    echo ✓ Unix build script found
) else (
    echo ✗ Unix build script missing
)

echo.
echo [4/6] Checking test structure...
if exist "src\test\java" (
    echo ✓ Java test directory found
) else (
    echo ✗ Java test directory missing
)

if exist "src\test\kotlin" (
    echo ✓ Kotlin test directory found
) else (
    echo ✗ Kotlin test directory missing
)

echo.
echo [5/6] Checking test files...
if exist "src\test\kotlin\net\ccbluex\liquidbounce\LiquidBounceTest.kt" (
    echo ✓ Kotlin test example found
) else (
    echo ✗ Kotlin test example missing
)

if exist "src\test\java\net\ccbluex\liquidbounce\UtilsTest.java" (
    echo ✓ Java test example found
) else (
    echo ✗ Java test example missing
)

echo.
echo [6/6] Checking documentation...
if exist "BUILD_AND_TEST_GUIDE.md" (
    echo ✓ Build guide found
) else (
    echo ✗ Build guide missing
)

echo.
echo ========================================
echo  Verification Complete
echo ========================================
echo.
echo Next steps:
echo 1. Open project in IntelliJ IDEA
echo 2. Look for "Build and Test" in run configurations
echo 3. Install Java 8 if needed
echo 4. Run: gradlew buildAndTest
echo.
pause
