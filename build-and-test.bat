@echo off
setlocal enabledelayedexpansion

:: LiquidBounce Legacy - Build and Test Script for Windows
:: This script provides a convenient way to build and test the project

echo.
echo ========================================
echo  LiquidBounce Legacy Build and Test
echo ========================================
echo.

:: Check if gradlew.bat exists
if not exist "gradlew.bat" (
    echo ERROR: gradlew.bat not found!
    echo Please make sure you're running this script from the project root directory.
    echo.
    pause
    exit /b 1
)

:: Check Java installation
java -version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Java is not installed or not in PATH!
    echo Please install Java 8 or higher and try again.
    echo.
    pause
    exit /b 1
)

echo Starting build and test process...
echo.

:: Run the buildAndTest task
call gradlew.bat buildAndTest

:: Check if the build was successful
if errorlevel 1 (
    echo.
    echo ========================================
    echo  BUILD FAILED!
    echo ========================================
    echo Please check the error messages above.
    echo.
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  BUILD SUCCESSFUL!
    echo ========================================
    echo.
    echo You can find the built JAR file in the build/libs directory.
    echo.
)

:: Ask if user wants to open the build directory
set /p "opendir=Do you want to open the build directory? (y/n): "
if /i "!opendir!"=="y" (
    if exist "build\libs" (
        explorer "build\libs"
    ) else (
        echo Build directory not found.
    )
)

echo.
echo Press any key to exit...
pause >nul
