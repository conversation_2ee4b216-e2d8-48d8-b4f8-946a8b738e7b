# LiquidBounce Legacy - 构建和测试指南

本项目已配置了多种构建和测试方式，方便开发和部署。

## 🚀 使用方式

### 1. IntelliJ IDEA 中使用

1. 打开项目后，点击右上角的运行配置下拉菜单
2. 选择 **"Build and Test"** 配置
3. 点击绿色的运行按钮

如果没有看到配置：
- 右键项目根目录 → "Reload Gradle Project"
- 重启 IntelliJ IDEA
- 检查 `.idea/runConfigurations/Build_and_Test.xml` 文件是否存在

### 2. 命令行使用

```bash
# Windows
./gradlew buildAndTest

# Linux/Mac
./gradlew buildAndTest
```

### 3. 脚本执行

#### Windows
双击 `build-and-test.bat` 文件

#### Linux/Mac
```bash
./build-and-test.sh
```

## 🔧 构建流程

构建过程包括以下步骤：
1. **Clean** - 清理之前的构建产物
2. **Test** - 运行所有测试（如果有）
3. **Build** - 编译和打包项目
4. **显示结果** - 显示构建产物位置和大小

## 🧪 测试结构

项目已配置基础测试结构：
- `src/test/java/` - Java 测试代码
- `src/test/kotlin/` - Kotlin 测试代码
- `src/test/resources/` - 测试资源文件

示例测试类：
- `LiquidBounceTest.kt` - Kotlin 测试示例
- `UtilsTest.java` - Java 测试示例

## ⚠️ 环境要求

- **Java 8** - 项目要求 Java 8 JDK
- **Gradle** - 使用项目自带的 Gradle Wrapper

### Java 版本问题解决

如果遇到 "Unsupported class file major version" 错误：

1. **安装 Java 8 JDK**
2. **设置 JAVA_HOME 环境变量**：
   ```bash
   # Windows
   set JAVA_HOME=C:\Program Files\Java\jdk1.8.0_xxx
   
   # Linux/Mac
   export JAVA_HOME=/path/to/java8
   ```
3. **在 IntelliJ 中配置项目 JDK**：
   - File → Project Structure → Project → Project SDK

## 📦 构建产物

成功构建后，JAR 文件位于：
```
build/libs/liquidbounce-b100.jar
```

## 🛠️ 自定义配置

### 修改构建任务

编辑 `build.gradle` 文件中的 `buildAndTest` 任务来自定义构建流程。

### 添加更多测试

在 `src/test/` 目录下添加新的测试类，构建系统会自动发现并运行它们。

## 📝 故障排除

### 常见问题

1. **Java 版本不匹配**
   - 确保使用 Java 8
   - 检查 JAVA_HOME 环境变量

2. **Gradle 权限问题**
   - Linux/Mac: `chmod +x gradlew`
   - Windows: 以管理员身份运行

3. **IDE 配置不显示**
   - 重新加载 Gradle 项目
   - 重启 IDE
   - 检查 `.idea/runConfigurations/` 目录

### 获取帮助

如果遇到问题，可以：
1. 查看构建日志中的详细错误信息
2. 运行 `./gradlew buildAndTest --stacktrace` 获取完整堆栈跟踪
3. 检查项目的 GitHub Issues

---

**Happy Coding! 🎉**
