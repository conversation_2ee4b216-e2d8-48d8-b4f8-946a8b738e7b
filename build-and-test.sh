#!/bin/bash

# LiquidBounce Legacy - Build and Test Script for Linux/Mac
# This script provides a convenient way to build and test the project

set -e  # Exit on any error

echo ""
echo "========================================"
echo "  LiquidBounce Legacy Build and Test"
echo "========================================"
echo ""

# Check if gradlew exists
if [ ! -f "./gradlew" ]; then
    echo "ERROR: gradlew not found!"
    echo "Please make sure you're running this script from the project root directory."
    echo ""
    exit 1
fi

# Make gradlew executable if it isn't already
if [ ! -x "./gradlew" ]; then
    echo "Making gradlew executable..."
    chmod +x ./gradlew
fi

# Check Java installation
if ! command -v java &> /dev/null; then
    echo "ERROR: Java is not installed or not in PATH!"
    echo "Please install Java 8 or higher and try again."
    echo ""
    exit 1
fi

# Display Java version
echo "Java version:"
java -version
echo ""

echo "Starting build and test process..."
echo ""

# Run the buildAndTest task
if ./gradlew buildAndTest; then
    echo ""
    echo "========================================"
    echo "  BUILD SUCCESSFUL!"
    echo "========================================"
    echo ""
    echo "You can find the built JAR file in the build/libs directory."
    echo ""
    
    # Ask if user wants to open the build directory (on systems with GUI)
    if command -v xdg-open &> /dev/null; then
        read -p "Do you want to open the build directory? (y/n): " -n 1 -r
        echo ""
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            if [ -d "build/libs" ]; then
                xdg-open build/libs
            else
                echo "Build directory not found."
            fi
        fi
    elif command -v open &> /dev/null; then
        # macOS
        read -p "Do you want to open the build directory? (y/n): " -n 1 -r
        echo ""
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            if [ -d "build/libs" ]; then
                open build/libs
            else
                echo "Build directory not found."
            fi
        fi
    fi
else
    echo ""
    echo "========================================"
    echo "  BUILD FAILED!"
    echo "========================================"
    echo "Please check the error messages above."
    echo ""
    exit 1
fi

echo ""
echo "Script completed successfully!"
