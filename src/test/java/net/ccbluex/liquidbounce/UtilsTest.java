package net.ccbluex.liquidbounce;

import org.junit.Test;
import static org.junit.Assert.*;

/**
 * Basic Java test class for LiquidBounce Legacy utilities
 * 
 * This demonstrates Java-based testing alongside Kotlin tests.
 * Add more specific utility tests as needed.
 */
public class UtilsTest {

    @Test
    public void testBasicJavaTest() {
        // Basic test to ensure Java testing works
        assertTrue("Basic Java test should pass", true);
    }

    @Test
    public void testStringOperations() {
        // Test basic string operations that might be used in the client
        String testString = "LiquidBounce";
        assertNotNull("Test string should not be null", testString);
        assertFalse("Test string should not be empty", testString.isEmpty());
        assertTrue("Test string should contain expected text", 
                  testString.contains("Liquid"));
    }

    @Test
    public void testMathOperations() {
        // Test basic math operations that might be used in client calculations
        int result = 2 + 2;
        assertEquals("Basic math should work correctly", 4, result);
        
        double distance = Math.sqrt(Math.pow(3, 2) + Math.pow(4, 2));
        assertEquals("Distance calculation should be correct", 5.0, distance, 0.001);
    }
}
